// Fill out your copyright notice in the Description page of Project Settings.

#include "MacMikePermissionRequest.h"

#if PLATFORM_MAC && !UE_BUILD_SHIPPING
#import <AVFoundation/AVFoundation.h>
#endif

// Sets default values for this component's properties
UMacMikePermissionRequest::UMacMikePermissionRequest()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	// ...
}

// Called when the game starts
void UMacMikePermissionRequest::BeginPlay()
{
	Super::BeginPlay();

#if PLATFORM_MAC && !UE_BUILD_SHIPPING
	// Request permission to access the camera and microphone.
	switch ([AVCaptureDevice authorizationStatusForMediaType : AVMediaTypeAudio])
	{
	case AVAuthorizationStatusAuthorized:
		{
			// The user has previously granted access
			break;
		}
	case AVAuthorizationStatusNotDetermined:
		{
			// The app hasn't yet asked the user for mic access.
			[AVCaptureDevice requestAccessForMediaType : AVMediaTypeAudio completionHandler : ^ (BOOL granted) {
			if (granted) {

			}
			}] ;

		}
	case AVAuthorizationStatusDenied:
		{

			// The user has previously denied access.

		}
	case AVAuthorizationStatusRestricted:
		{

			// The user can't grant access due to restrictions.

		}
	}
#endif
}

// Called every frame
void UMacMikePermissionRequest::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// ...
}