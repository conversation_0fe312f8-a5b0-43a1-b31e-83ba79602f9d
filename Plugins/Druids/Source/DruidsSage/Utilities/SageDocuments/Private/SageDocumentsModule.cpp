#include "SageDocumentsModule.h"
#include "SageDocumentsUtility.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"

#define LOCTEXT_NAMESPACE "FSageDocumentsModule"

void FSageDocumentsModule::StartupModule()
{
    // Ensure the docs source folder exists in the project root
    EnsureDocsSourceFolderExists();
    
    // Ensure the README exists in the docs source folder
    EnsureReadmeExists(); // README stays only in DruidsSageDocsSource
    
    // Sync documents from source to saved location
    SyncDocuments();
}

void FSageDocumentsModule::ShutdownModule()
{
    
}

void FSageDocumentsModule::EnsureDocsSourceFolderExists()
{
    FString DocsSourcePath = GetDocsSourcePath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*DocsSourcePath))
    {
        PlatformFile.CreateDirectoryTree(*DocsSourcePath);
        UE_LOG(LogTemp, Log, TEXT("Created DruidsSageDocsSource directory at: %s"), *DocsSourcePath);
    }
}

void FSageDocumentsModule::EnsureReadmeExists()
{
    FString DocsSourcePath = GetDocsSourcePath();
    FString ReadmeDestPath = FPaths::Combine(DocsSourcePath, TEXT("README_SageDocuments.md"));
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    // Only copy if the README doesn't already exist in the docs source folder
    if (!PlatformFile.FileExists(*ReadmeDestPath))
    {
        FString PluginReadmePath = GetPluginReadmePath();
        
        if (PlatformFile.FileExists(*PluginReadmePath))
        {
            PlatformFile.CopyFile(*ReadmeDestPath, *PluginReadmePath);
            UE_LOG(LogTemp, Log, TEXT("Copied README to DruidsSageDocsSource: %s"), *ReadmeDestPath);
        }
        else
        {
            // Create a basic README if the plugin README doesn't exist
            FString BasicReadme = TEXT("# Sage Documents\n\n")
                TEXT("This folder contains documents that Sage can access and reference.\n\n")
                TEXT("## How to Use\n\n")
                TEXT("1. Place any documents you want Sage to know about in this folder\n")
                TEXT("2. Supported file types: .md, .txt, .json, .xml, .html, .csv\n")
                TEXT("3. Documents will be automatically synced to the Saved folder\n")
                TEXT("4. Sage can list and read these documents through the 'list_docs' and 'get_doc_contents' commands\n\n")
                TEXT("## Examples\n\n")
                TEXT("- Project documentation\n")
                TEXT("- API references\n")
                TEXT("- Code style guides\n")
                TEXT("- Requirements documents\n")
                TEXT("- Any other reference material you want Sage to access\n");
                
            FFileHelper::SaveStringToFile(BasicReadme, *ReadmeDestPath);
            UE_LOG(LogTemp, Log, TEXT("Created basic README at: %s"), *ReadmeDestPath);
        }
    }
}

void FSageDocumentsModule::SyncDocuments()
{
    USageDocumentsUtility::SyncDocuments();
}

FString FSageDocumentsModule::GetProjectRootPath() const
{
    return FPaths::ProjectDir();
}

FString FSageDocumentsModule::GetDocsSourcePath() const
{
    return FPaths::Combine(GetProjectRootPath(), TEXT("DruidsSageDocsSource"));
}

FString FSageDocumentsModule::GetSavedDocsPath() const
{
    return USageDocumentsUtility::GetSavedDocsPath();
}

FString FSageDocumentsModule::GetPluginReadmePath() const
{
    // This would be the path to a README in the plugin's Content folder
    FString PluginDir = FPaths::Combine(FPaths::ProjectPluginsDir(), TEXT("Druids"));
    return FPaths::Combine(PluginDir, TEXT("Content"), TEXT("README_SageDocuments.md"));
}

#undef LOCTEXT_NAMESPACE
    
IMPLEMENT_MODULE(FSageDocumentsModule, SageDocuments)
