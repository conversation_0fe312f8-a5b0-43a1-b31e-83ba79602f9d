#include "SageChatMessageHelper.h"
#include "SimpleJSON.h"
#include "DruidsSageHelper.h"

TSharedPtr<FJsonValue> FSageChatMessageHelper::GetMessageJson(
    const FDruidsSageChatMessage& Message, 
    bool bIncludeContext)
{
    SimpleJSON ResponseJSON;
    ResponseJSON["role"] = UDruidsSageHelper::RoleToName(Message.GetRole()).ToString().ToLower();

    // Always use ContentArray if available
    if (!Message.GetContentArray().IsEmpty())
    {
        ResponseJSON["content"] = Message.GetContentArray();
    }
    else if (!Message.GetChatContent().IsEmpty())
    {
        ResponseJSON["content"][0]["type"] = TEXT("text");
        ResponseJSON["content"][0]["text"] = Message.GetChatContent();
    }
    else
    {
        TArray<TSharedPtr<FJsonValue>> EmptyContentArray;
        ResponseJSON["content"] = EmptyContentArray;
    }
    
    if (bIncludeContext)
    {
        if (!Message.GetUserFocusContext().IsEmpty())
        {
            int32 currentIndex = ResponseJSON["content"].AsNativeArray().Num();
            ResponseJSON["content"][currentIndex]["type"] = TEXT("user_focus_context");
            ResponseJSON["content"][currentIndex]["user_focus_context"] = Message.GetUserFocusContext();
        }

        if (!Message.GetUserChatHistory().IsEmpty())
        {
            int32 currentIndex = ResponseJSON["content"].AsNativeArray().Num();
            ResponseJSON["content"][currentIndex]["type"] = TEXT("chat_memories");
            ResponseJSON["content"][currentIndex]["chat_memories"] = Message.GetUserChatHistory();
        }
    }

    if (!Message.GetThinkingArray().IsEmpty())
    {
        ResponseJSON["thinking"] = Message.GetThinkingArray();
    }

    return ResponseJSON.GetJsonValue();
}