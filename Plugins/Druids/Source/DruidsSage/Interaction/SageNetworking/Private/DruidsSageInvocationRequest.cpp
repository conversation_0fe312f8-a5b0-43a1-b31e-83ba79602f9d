#include "DruidsSageInvocationRequest.h"

#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/ScopeTryLock.h>
#include <Async/Async.h>
#include <HttpModule.h>

#include "LogDruids.h"

#include "DruidsSageHelper.h"
#include "DruidsSageSettings.h"

#include "SimpleJSON.h"

#if WITH_EDITOR
#include <Editor.h>
#endif

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageInvocationRequest)
#endif

#if WITH_EDITOR
UDruidsSageInvocationRequest* UDruidsSageInvocationRequest::EditorTask(const FString& Prompt)
{
	UDruidsSageInvocationRequest* const NewAsyncTask = SendInvocation(
		GEditor->GetEditorWorldContext().World(), Prompt);

	NewAsyncTask->bIsEditorTask = true;

	return NewAsyncTask;
}
#endif

UDruidsSageInvocationRequest* UDruidsSageInvocationRequest::SendInvocation(
	UObject* const WorldContextObject,
	const FString& Prompt)
{
	UDruidsSageInvocationRequest* const NewAsyncTask = NewObject<UDruidsSageInvocationRequest>();
	NewAsyncTask->Prompt = Prompt;

	NewAsyncTask->RegisterWithGameInstance(WorldContextObject);

	return NewAsyncTask;
}

bool UDruidsSageInvocationRequest::CanActivateTask()
{
	if (!Super::CanActivateTask())
	{
		return false;
	}

	if (Prompt.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Can't activate task: Invalid Prompt."), *FString(__FUNCTION__), GetUniqueID());
		return false;
	}

	return true;
}

bool UDruidsSageInvocationRequest::CanBindProgress() const
{
	//Always streaming
	return true;
}

FString UDruidsSageInvocationRequest::GetEndpointURL() const
{
	return TEXT("https://groveserver.replit.app/sprout/caller/v1");
}

void UDruidsSageInvocationRequest::CustomizeRequestHeaders()
{
	Super::CustomizeRequestHeaders();
}

FString UDruidsSageInvocationRequest::SetRequestContent()
{
	FScopeLock Lock(&Mutex);

	if (!HttpRequest.IsValid())
	{
		return FString();
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Mounting content"), *FString(__FUNCTION__), GetUniqueID());

	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();
	
	JsonRequest->SetStringField("endpoint", "webhook/sage/invocation_request/v1");

	// Create payload object with the prompt
	const TSharedPtr<FJsonObject> PayloadObject = MakeShared<FJsonObject>();
	PayloadObject->SetStringField("prompt", Prompt);

	JsonRequest->SetObjectField("payload", PayloadObject);

	FString OutputString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer);

	return OutputString;
}

void UDruidsSageInvocationRequest::OnProgressUpdated(const FString& Content, int32 BytesSent, int32 BytesReceived)
{
	FScopeLock Lock(&Mutex);

	if (Content.IsEmpty())
	{
		return;
	}

	TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Progress Updated"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: %s; Bytes Sent: %d; Bytes Received: %d"), *FString(__FUNCTION__), GetUniqueID(),
	       StreamedResponses.IsEmpty() ? TEXT("<none>") : *StreamedResponses.Top(), BytesSent, BytesReceived);

	ProcessStreamedResponses(StreamedResponses);

	if (!Response.bSuccess)
	{
		return;
	}

	if (!bInitialized)
	{
		bInitialized = true;

		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ProgressStarted.Broadcast(Response);
		});
	}

	AsyncTask(ENamedThreads::GameThread, [this]
	{
		FScopeTryLock Lock(&Mutex);

		if (Lock.IsLocked())
		{
			ProgressUpdated.Broadcast(Response);
		}
	});
}

void UDruidsSageInvocationRequest::OnProgressCompleted(const FString& Content, const bool bWasSuccessful)
{
	FScopeLock Lock(&Mutex);

	if (!bWasSuccessful || Content.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			RequestFailed.Broadcast();
		});

		return;
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Process Completed"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: \nBEGIN>>>\n%s\n<<<END"), *FString(__FUNCTION__), GetUniqueID(), *Content);

	const TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);
	ProcessStreamedResponses(StreamedResponses);

	if (Response.bSuccess)
	{
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);

			ProcessCompleted.Broadcast(Response);
		});
	}
	else
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ErrorReceived.Broadcast(Response);
		});
	}
}

TArray<FString> UDruidsSageInvocationRequest::GetStreamedResponsesFromContent(const FString& Content)
{
	TArray<FString> Deltas_In, Deltas_Out;
	Content.ParseIntoArray(Deltas_In, TEXT("\n\n"));
	for (FString Delta_In : Deltas_In)
	{
		if (Delta_In.StartsWith("data:"))
		{
			if (!Delta_In.StartsWith("data: [done]"))
			{
				Deltas_Out.Add(Delta_In.Replace(TEXT("data: "), TEXT("")));
			}
		}
	}

	return Deltas_Out;
}

void UDruidsSageInvocationRequest::ProcessStreamedResponses(const TArray<FString>& StreamedResponses)
{
	FScopeLock Lock(&Mutex);

	Response.bSuccess = true;

	Response.Choices.Empty(StreamedResponses.Num());
	for (const FString& StreamedResponse : StreamedResponses)
	{
		ProcessStreamedResponse(StreamedResponse);
	}
}

void UDruidsSageInvocationRequest::ProcessStreamedResponse(const FString& StreamedResponse)
{
	FScopeLock Lock(&Mutex);

	if (StreamedResponse.IsEmpty())
	{
		return;
	}

	SimpleJSON ResponseJSON(StreamedResponse);

	if (const FString RequestType = ResponseJSON["type"].AsString(); !RequestType.IsEmpty() && ResponseJSON["payload"].IsValid())
	{
		TSharedPtr<FJsonObject> Payload = ResponseJSON["payload"].GetJsonObject();
		const FString ThreadId = ResponseJSON["thread_id"].AsString();
		const FString RequestId = ResponseJSON["request_id"].AsString();
		const FString ResponseUrl = ResponseJSON["response_url"].AsString();
		ProcessGenericRequest(RequestType, Payload, ThreadId, RequestId, ResponseUrl);
		return;
	}

	if (ResponseJSON["status"].AsString() == TEXT("success"))
	{
		Response.bSuccess = true;
		Response.SuccessSummary = ResponseJSON["summary"].AsString();
		return;
	}

	Response.bSuccess = false;
}

void UDruidsSageInvocationRequest::ProcessGenericRequest(const FString& RequestType,
                                                        const TSharedPtr<FJsonObject>& Payload,
                                                        const FString& ThreadId,
                                                        const FString& RequestId,
                                                        const FString& ResponseUrl)
{
	// Delegate to the utility class
	GenericRequestHandler.ProcessGenericRequest(RequestType, Payload, ThreadId, RequestId, ResponseUrl, CommonOptions);
}
