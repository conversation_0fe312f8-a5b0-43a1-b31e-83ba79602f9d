# SageExtension Editor Overrides System

This document explains how to use the new SageExtensionEditorOverrides system to customize the Sage Extension Editor widget path and class.

## Overview

The SageExtensionEditorOverrides system allows you to:
- **Customize Widget Path**: Override the default path to the Editor Utility Widget Blueprint
- **Customize Widget Class**: Override the base C++ class used for the Sage Extension Editor
- **Blueprint Configuration**: Configure overrides entirely through Blueprint without code changes

This follows the same pattern as ChatWidgetOverrides, providing a consistent approach to customization across the DruidsSage system.

## How It Works

### 1. Override Class Structure
The system consists of:
- **USageExtensionEditorOverrides**: Base C++ class that can be overridden in Blueprint
- **FSageExtensionsModule**: Loads and provides access to the overrides instance
- **FDruidsSageEditorModule**: Uses the overrides when opening the Sage Extension Editor

### 2. Default Behavior
Without any overrides:
- **Default Path**: `/Druids/Code/EditorWidgets/EUW_SageExtensionEditor.EUW_SageExtensionEditor`
- **Default Class**: `USageExtensionEditorWidget`

### 3. Override Loading
The system automatically loads overrides from:
- **Blueprint Path**: `/Druids/SageExtensions/SageExtensionEditorOverrides.SageExtensionEditorOverrides_C`

## Creating Override Blueprints

### Step 1: Create the Override Blueprint
1. In the Content Browser, navigate to `/Druids/SageExtensions/`
2. Right-click and select "Blueprint Class"
3. Choose "SageExtensionEditorOverrides" as the parent class
4. Name it `SageExtensionEditorOverrides`

### Step 2: Configure the Override
In the Blueprint:

#### Option A: Set Default Properties
- Set **Default Sage Extension Editor Widget Path** to your custom widget path
- Set **Default Sage Extension Editor Widget Class** to your custom C++ class (if any)

#### Option B: Override Functions (Advanced)
Override these functions for dynamic behavior:
- **Get Sage Extension Editor Widget Path**: Return custom path based on runtime conditions
- **Get Sage Extension Editor Widget Class**: Return custom class based on runtime conditions

### Step 3: Example Configurations

#### Custom Widget Path Only
```
Default Sage Extension Editor Widget Path: "/MyProject/CustomWidgets/MyCustomSageExtensionEditor.MyCustomSageExtensionEditor"
Default Sage Extension Editor Widget Class: (leave empty)
```

#### Custom Widget Class Only
```
Default Sage Extension Editor Widget Path: (leave empty)
Default Sage Extension Editor Widget Class: MyCustomSageExtensionEditorWidget
```

#### Both Custom Path and Class
```
Default Sage Extension Editor Widget Path: "/MyProject/CustomWidgets/MyAdvancedSageExtensionEditor.MyAdvancedSageExtensionEditor"
Default Sage Extension Editor Widget Class: MyAdvancedSageExtensionEditorWidget
```

## Creating Custom Editor Widgets

### Step 1: Create Custom C++ Class (Optional)
If you need custom C++ functionality:

```cpp
UCLASS(BlueprintType, Blueprintable)
class MYPROJECT_API UMyCustomSageExtensionEditorWidget : public USageExtensionEditorWidget
{
    GENERATED_BODY()

public:
    // Add your custom functionality here
    
    UFUNCTION(BlueprintCallable, Category = "Custom Sage Extension Editor")
    void MyCustomFunction();
};
```

### Step 2: Create Custom Editor Utility Widget Blueprint
1. Create a new Editor Utility Widget Blueprint
2. Set the parent class to your custom C++ class (or USageExtensionEditorWidget)
3. Design your custom UI
4. Implement the required functionality

### Step 3: Update Override Blueprint
Set the custom path and/or class in your SageExtensionEditorOverrides Blueprint.

## Usage Workflow

1. **Create Override Blueprint** at `/Druids/SageExtensions/SageExtensionEditorOverrides`
2. **Configure custom paths/classes** in the Blueprint
3. **Create custom Editor Utility Widget** (if using custom path)
4. **Create custom C++ class** (if using custom class)
5. **Test the integration** by opening a SageExtension Blueprint and clicking the toolbar button

## Technical Notes

### File Locations
- **Override Blueprint**: `/Druids/SageExtensions/SageExtensionEditorOverrides.SageExtensionEditorOverrides_C`
- **Default Widget**: `/Druids/Code/EditorWidgets/EUW_SageExtensionEditor.EUW_SageExtensionEditor`

### Logging
The system provides detailed logging:
- Loading override Blueprint success/failure
- Using custom vs default paths
- Widget loading success/failure

### Fallback Behavior
If overrides fail to load or return invalid values:
- Falls back to default path and class
- Logs warnings for debugging
- Continues with standard functionality

### Cache Invalidation
Similar to ChatWidgetOverrides, the overrides are loaded fresh each time to handle Blueprint recompilation.

## Troubleshooting

### Override Blueprint Not Found
- Ensure the Blueprint is at the exact path: `/Druids/SageExtensions/SageExtensionEditorOverrides.SageExtensionEditorOverrides_C`
- Check that the parent class is set to `USageExtensionEditorOverrides`

### Custom Widget Not Loading
- Verify the custom widget path is correct
- Ensure the custom widget inherits from `USageExtensionEditorWidget` (or your custom class)
- Check the logs for specific error messages

### Custom Class Not Found
- Ensure your custom C++ class is properly compiled
- Verify the class inherits from `USageExtensionEditorWidget`
- Check that the module dependencies are correct

## Migration from Hard-coded Paths

If you were previously using the hard-coded path system:
1. Create a SageExtensionEditorOverrides Blueprint
2. Set the Default Sage Extension Editor Widget Path to your existing widget path
3. Remove any hard-coded path modifications from C++ code
4. Test that the system works as expected

The new system is fully backward compatible - if no overrides are found, it uses the original default path.

 
 