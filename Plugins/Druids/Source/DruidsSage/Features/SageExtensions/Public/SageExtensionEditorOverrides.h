#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "SageExtensionEditorWidget.h"
#include "SageExtensionEditorOverrides.generated.h"

/**
 * SageExtensionEditorOverrides class that can be overridden in Blueprint to specify
 * which USageExtensionEditorWidget class to instantiate and what path to use for the Editor Utility Widget.
 * This allows for Blueprint customization of the Sage Extension Editor.
 */
UCLASS(Blueprintable, BlueprintType)
class SAGEEXTENSIONS_API USageExtensionEditorOverrides : public UObject
{
	GENERATED_BODY()

public:
	USageExtensionEditorOverrides();

	/**
	 * Gets the widget class to instantiate for the Sage Extension Editor.
	 * Can be overridden in Blueprint to return a custom USageExtensionEditorWidget subclass.
	 * @return The USageExtensionEditorWidget class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Sage Extension Editor Overrides")
	TSubclassOf<USageExtensionEditorWidget> GetSageExtensionEditorWidgetClass() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetSageExtensionEditorWidgetClass instead.
	 */
	virtual TSubclassOf<USageExtensionEditorWidget> GetSageExtensionEditorWidgetClass_Implementation() const;

protected:
	/**
	 * The default Sage Extension Editor widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use USageExtensionEditorWidget.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sage Extension Editor Overrides")
	TSubclassOf<USageExtensionEditorWidget> DefaultSageExtensionEditorWidgetClass;
};
