#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

// Forward declarations
class USageExtensionEditorOverrides;

class SAGEEXTENSIONS_API FSageExtensionsModule : public IModuleInterface
{
public:
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /**
     * Gets the SageExtensionEditorOverrides instance for use by other modules.
     * @return The SageExtensionEditorOverrides instance, or nullptr if not loaded
     */
    USageExtensionEditorOverrides* GetSageExtensionEditorOverrides();

private:
    /**
     * Loads the SageExtensionEditorOverrides from the hard-coded plugin content location.
     * @return The loaded SageExtensionEditorOverrides instance, or nullptr if not found or failed to load
     */
    static USageExtensionEditorOverrides* LoadSageExtensionEditorOverrides();
};
