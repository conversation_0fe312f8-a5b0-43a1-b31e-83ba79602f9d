#pragma once

#include "CoreMinimal.h"
#include "EditorUtilityWidgetBlueprint.h"
#include "SageExtension.h"
#include "SageExtensionTypes.h"

#include "SageExtensionEditorWidget.generated.h"

/**
 * Base class for SageExtension Editor Utility Widget
 * Provides C++ functionality that can be extended in Blueprint
 */
UCLASS(BlueprintType, Blueprintable)
class SAGEEXTENSIONS_API USageExtensionEditorWidget : public UEditorUtilityWidgetBlueprint
{
	GENERATED_BODY()

public:
	USageExtensionEditorWidget(const FObjectInitializer& ObjectInitializer);

public:
	/**
	 * Refreshes the widget with current SageExtension data
	 * Call this when the active Blueprint changes or when data is updated
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	void RefreshWidget();

	/**
	 * Gets the currently active SageExtension
	 * @return The active SageExtension, or nullptr if none is active
	 */
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sage Extension Editor")
	USageExtension* GetCurrentSageExtension() const;

	/**
	 * Checks if a SageExtension Blueprint is currently active
	 * @return True if a SageExtension Blueprint is being edited
	 */
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sage Extension Editor")
	bool IsSageExtensionActive() const;

	/**
	 * Gets the name of the current SageExtension Blueprint
	 * @return The Blueprint name, or empty string if none is active
	 */
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sage Extension Editor")
	FString GetCurrentBlueprintName() const;

	/**
	 * Gets the extension definition from the current SageExtension
	 * @return The extension definition
	 */
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sage Extension Editor")
	FDruidsSageExtensionDefinition GetExtensionDefinition() const;

	/**
	 * Refreshes the current SageExtension (rediscovers actions and queries)
	 * @return True if refresh was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	bool RefreshSageExtension();

	/**
	 * Updates the extension name
	 * @param NewName The new extension name
	 * @return True if update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	bool UpdateExtensionName(const FString& NewName);

	/**
	 * Updates the extension description
	 * @param NewDescription The new extension description
	 * @return True if update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	bool UpdateExtensionDescription(const FString& NewDescription);

	/**
	 * Updates a parameter description for an Action
	 * @param ActionFunctionName The name of the action function
	 * @param ParameterName The name of the parameter
	 * @param NewDescription The new description
	 * @return True if update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	bool UpdateActionParameterDescription(const FString& ActionFunctionName, const FString& ParameterName, const FString& NewDescription);

	/**
	 * Updates a parameter description for a Query
	 * @param QueryFunctionName The name of the query function
	 * @param ParameterName The name of the parameter
	 * @param NewDescription The new description
	 * @return True if update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor")
	bool UpdateQueryParameterDescription(const FString& QueryFunctionName, const FString& ParameterName, const FString& NewDescription);

protected:
	/**
	 * Blueprint event called when the widget should refresh its data
	 * Override this in Blueprint to update your UI elements
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Editor")
	void OnRefreshWidget();

	/**
	 * Blueprint event called when a SageExtension becomes active
	 * @param SageExtension The newly active SageExtension
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Editor")
	void OnSageExtensionActivated(USageExtension* SageExtension);

	/**
	 * Blueprint event called when no SageExtension is active
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "Sage Extension Editor")
	void OnNoSageExtensionActive();

private:
	// Timer handle for periodic refresh checks
	FTimerHandle RefreshTimerHandle;

	// Last known active SageExtension (to detect changes)
	UPROPERTY()
	TWeakObjectPtr<USageExtension> LastActiveSageExtension;

	/**
	 * Timer callback to check for changes in active SageExtension
	 */
	void CheckForActiveExtensionChanges();
};
