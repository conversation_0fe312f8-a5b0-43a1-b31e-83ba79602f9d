#include "SageExtensionsModule.h"
#include "SageExtensionEditorOverrides.h"

// Asset loading includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

#if WITH_EDITOR
#include "Editor.h"
#endif

#define LOCTEXT_NAMESPACE "FSageExtensionsModule"

void FSageExtensionsModule::StartupModule()
{
	// SageMain module will handle the Blueprint context hookup
}

void FSageExtensionsModule::ShutdownModule()
{
	// SageMain module handles cleanup
}

USageExtensionEditorOverrides* FSageExtensionsModule::GetSageExtensionEditorOverrides()
{
	return LoadSageExtensionEditorOverrides();
}

USageExtensionEditorOverrides* FSageExtensionsModule::LoadSageExtensionEditorOverrides()
{
	USageExtensionEditorOverrides* SageExtensionEditorOverrides = nullptr;

	// Hard-coded path to the SageExtensionEditorOverrides Blueprint in the plugin's Content folder
	const FString BlueprintPath = TEXT("/Druids/SageExtensionWidgets/SageExtensionEditorOverrides.SageExtensionEditorOverrides_C");

	// Try to load the Blueprint class - use nullptr as Outer for UE 5.6 compatibility
	UClass* BlueprintClass = LoadClass<USageExtensionEditorOverrides>(nullptr, *BlueprintPath);
	if (!BlueprintClass)
	{
		// Blueprint not found or failed to load
		UE_LOG(LogTemp, Warning, TEXT("SageExtensionEditorOverrides Blueprint not found at path: %s"), *BlueprintPath);
		return nullptr;
	}

	// Create an instance of the Blueprint class using the transient package as outer
#if WITH_EDITOR
	if (GEditor)
	{
		UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
		SageExtensionEditorOverrides = NewObject<USageExtensionEditorOverrides>(EditorWorld, BlueprintClass);
	}
#endif

	if (!SageExtensionEditorOverrides)
	{
		SageExtensionEditorOverrides = NewObject<USageExtensionEditorOverrides>((UObject*)GetTransientPackage(), BlueprintClass);
	}

	if (!SageExtensionEditorOverrides)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create instance of SageExtensionEditorOverrides Blueprint"));
		return nullptr;
	}

	UE_LOG(LogTemp, Log, TEXT("Successfully loaded SageExtensionEditorOverrides from: %s"), *BlueprintPath);
	return SageExtensionEditorOverrides;
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageExtensionsModule, SageExtensions)