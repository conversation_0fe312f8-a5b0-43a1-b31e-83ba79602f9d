#include "SageExtensionEditorOverrides.h"
#include "SageExtensionEditorWidget.h"

USageExtensionEditorOverrides::USageExtensionEditorOverrides()
{
	// Initialize with nullptr/empty - will use base class and default path by default
	DefaultSageExtensionEditorWidgetClass = nullptr;
}

TSubclassOf<USageExtensionEditorWidget> USageExtensionEditorOverrides::GetSageExtensionEditorWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultSageExtensionEditorWidgetClass;
}
